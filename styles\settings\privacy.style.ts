import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    gap: 0
  },
  sectionTitle: {
    color: "#90A0AE",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    marginBottom: 8
  },
  privacyItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 0,
    paddingVertical: 16,
    gap: 8
  },
  privacyTitle: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20
  },
  privacySubtitle: {
    color: "#90A0AE",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    marginTop: 4
  },
  privacyControl: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8
  },
  privacyStatus: {
    color: "#90A0AE",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 10,
    fontWeight: "400",
    lineHeight: 18,
    width: 120,
    textAlign: "right"
  },

  toggleBase: {
    flexDirection: "row",
    alignItems: "center",
    padding: 2,
    width: 44,
    height: 24,
    borderRadius: 12
  },
  toggleEnabled: {
    backgroundColor: "#00C896",
    justifyContent: "flex-end"
  },
  toggleDisabled: {
    backgroundColor: "#4A5568",
    justifyContent: "flex-start"
  },
  toggleButton: {
    width: 24,
    height: 24,
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    shadowColor: "rgba(0, 0, 0, 0.1)",
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 3
  },
  toggleButtonEnabled: {
    // Position is handled by justifyContent in toggleBase
  },
  toggleButtonDisabled: {
    // Position is handled by justifyContent in toggleBase
  },
  restoreButton: {
    backgroundColor: "transparent",
    paddingVertical: 10,
    paddingHorizontal: 18,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 258,
    borderRadius: 8,
    height: 44
  },
  restoreButtonText: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24
  }
});

export default styles;
